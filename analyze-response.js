const fetch = require('node-fetch');
const fs = require('fs');

async function analyzeResponse() {
  try {
    console.log('Analyzing TSC response for ID: 11209837');
    
    const response = await fetch('http://localhost:3002/api/check-registration', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ idNumber: '11209837' })
    });
    
    const result = await response.json();
    
    if (result.success) {
      // The rawHtml in the result is truncated to 1000 chars, let's get the full response
      console.log('Raw HTML length from API:', result.rawHtml.length);

      // Make a direct request to get the full HTML
      console.log('Making direct request to get full HTML...');
      const directResponse = await fetch('http://localhost:3002/api/check-registration', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ idNumber: '11209837' })
      });

      const directResult = await directResponse.json();
      const fullHtml = directResult.rawHtml;

      fs.writeFileSync('tsc-response.html', fullHtml);
      console.log('Full HTML response saved to tsc-response.html');
      
      // Look for common patterns that might indicate registration status
      const patterns = [
        /registration/gi,
        /status/gi,
        /teacher/gi,
        /name/gi,
        /found/gi,
        /not found/gi,
        /error/gi,
        /success/gi,
        /valid/gi,
        /invalid/gi,
        /record/gi,
        /11209837/gi
      ];
      
      console.log('\n=== Pattern Analysis ===');
      patterns.forEach(pattern => {
        const matches = fullHtml.match(pattern);
        if (matches) {
          console.log(`${pattern.source}: ${matches.length} matches`);
        }
      });
      
      // Look for table structures or specific content areas
      console.log('\n=== Structure Analysis ===');
      console.log('Contains table:', fullHtml.includes('<table>'));
      console.log('Contains form:', fullHtml.includes('<form>'));
      console.log('Contains div:', fullHtml.includes('<div>'));
      console.log('Contains alert:', fullHtml.includes('alert'));
      console.log('Contains message:', fullHtml.includes('message'));
      
      // Extract any text content between common HTML tags
      const textContent = fullHtml.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim();
      console.log('\n=== Text Content Preview (first 500 chars) ===');
      console.log(textContent.substring(0, 500));
      
    } else {
      console.log('API call failed:', result.error);
    }
    
  } catch (error) {
    console.error('Analysis failed:', error.message);
  }
}

analyzeResponse();
