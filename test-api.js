const fetch = require('node-fetch');

async function testTSCAPI() {
  try {
    console.log('Testing TSC API with ID: 11209837');
    
    const response = await fetch('http://localhost:3002/api/check-registration', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ idNumber: '11209837' })
    });
    
    console.log('Response status:', response.status);
    console.log('Response headers:', response.headers.raw());
    
    const result = await response.json();
    
    console.log('\n=== API Response ===');
    console.log('Success:', result.success);
    
    if (result.success && result.data) {
      console.log('Status:', result.data.status);
      console.log('Message:', result.data.message);
      console.log('Details:', result.data.details);
      
      console.log('\n=== Raw HTML Preview ===');
      console.log(result.rawHtml.substring(0, 500) + '...');
    } else {
      console.log('Error:', result.error);
    }
    
  } catch (error) {
    console.error('Test failed:', error.message);
  }
}

testTSCAPI();
