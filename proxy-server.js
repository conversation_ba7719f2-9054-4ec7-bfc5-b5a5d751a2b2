const express = require('express');
const cors = require('cors');
const fetch = require('node-fetch');
const path = require('path');

const app = express();
const PORT = 3002;

// Enable CORS for all routes
app.use(cors());

// Parse JSON and form data
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Serve static files (your HTML and JS files)
app.use(express.static('.'));

// Proxy endpoint for TSC registration check
app.post('/api/check-registration', async (req, res) => {
  try {
    const { idNumber } = req.body;
    
    if (!idNumber) {
      return res.status(400).json({ error: 'ID number is required' });
    }

    const tscUrl = 'http://tsconline.tsc.go.ke/register/registration-status';
    
    // First, get the page to extract CSRF token
    console.log('Fetching TSC page for CSRF token...');
    const initialResponse = await fetch(tscUrl);
    const html = await initialResponse.text();
    
    // Extract CSRF token
    const csrfMatch = html.match(/<meta name="csrf-token" content="([^"]+)">/);
    const csrfToken = csrfMatch ? csrfMatch[1] : '';
    
    console.log('CSRF Token found:', csrfToken ? 'Yes' : 'No');
    
    // Prepare form data
    const formData = new URLSearchParams();
    formData.append('id_no', idNumber);
    if (csrfToken) {
      formData.append('_csrf', csrfToken);
    }
    
    // Submit the form
    console.log('Submitting registration check for ID:', idNumber);
    const submitResponse = await fetch(tscUrl, {
      method: 'POST',
      body: formData,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Referer': tscUrl
      }
    });

    console.log('Submit response status:', submitResponse.status);
    console.log('Submit response headers:', submitResponse.headers.raw());

    if (!submitResponse.ok) {
      throw new Error(`HTTP error! Status: ${submitResponse.status}`);
    }

    const resultHtml = await submitResponse.text();
    console.log('Response length:', resultHtml.length);
    
    // Try to parse the result to extract meaningful information
    const registrationInfo = parseRegistrationResult(resultHtml);
    
    res.json({
      success: true,
      data: registrationInfo,
      rawHtml: resultHtml, // Full HTML response
      htmlLength: resultHtml.length
    });
    
  } catch (error) {
    console.error('Error checking registration:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Function to parse the registration result
function parseRegistrationResult(html) {
  const result = {
    status: 'unknown',
    message: '',
    details: {}
  };

  // Check if we're back at the search form (indicates no results or error)
  if (html.includes('Enter ID number and search')) {
    result.status = 'no_results';
    result.message = 'No registration record found for this ID number, or the search returned to the form';
    return result;
  }

  // Look for positive indicators
  if (html.includes('Registration Details') || html.includes('Teacher Details') ||
      html.includes('Registration Number') || html.includes('Employee Number')) {
    result.status = 'found';
    result.message = 'Registration information found';

    // Try to extract specific details
    const nameMatch = html.match(/Name[:\s]*([^<\n]+)/i);
    if (nameMatch) {
      result.details.name = nameMatch[1].trim();
    }

    const regNumberMatch = html.match(/Registration\s*Number[:\s]*([^<\n]+)/i);
    if (regNumberMatch) {
      result.details.registrationNumber = regNumberMatch[1].trim();
    }

    const employeeNumberMatch = html.match(/Employee\s*Number[:\s]*([^<\n]+)/i);
    if (employeeNumberMatch) {
      result.details.employeeNumber = employeeNumberMatch[1].trim();
    }

    return result;
  }

  // Look for explicit error messages
  if (html.includes('not found') || html.includes('No record') ||
      html.includes('Invalid ID') || html.includes('does not exist')) {
    result.status = 'not_found';
    result.message = 'No registration record found for this ID number';
    return result;
  }

  // Look for error indicators
  if (html.includes('error') || html.includes('Error') || html.includes('failed')) {
    result.status = 'error';
    result.message = 'Error occurred while checking registration';
    return result;
  }

  // Check if the response contains the search form but also has results
  if (html.includes('CHECK REGISTRATION STATUS') && html.includes('TEACHERS SERVICE COMMISSION')) {
    result.status = 'form_displayed';
    result.message = 'Search form displayed - may indicate no results found or need to search again';
    return result;
  }

  // Default case
  result.status = 'unknown';
  result.message = 'Unable to determine registration status from response';
  return result;
}

// Serve the main HTML file
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'tsc_checker.html'));
});

app.listen(PORT, () => {
  console.log(`Proxy server running on http://localhost:${PORT}`);
  console.log(`Open your browser and go to http://localhost:${PORT} to use the TSC checker`);
});
